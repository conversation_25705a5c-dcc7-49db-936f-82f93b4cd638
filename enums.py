from pydantic import BaseModel, model_validator
import json
from constants import DEFAULT_FIRST_MESSAGE, DEFAULT_SYSTEM_PROMPT


class LoadData(BaseModel):

    rate: str = ""
    rate_per_mile: str = ""
    loaded_miles: str = ""
    deadhead_miles: str = ""
    pickup_location: str = ""
    delivery_location: str = ""
    pickup_date: str = ""
    commodity_type: str = ""
    weight: str = ""
    truck_size: str = ""
    broker_name: str = ""
    broker_mc: str = ""
    ref: str = ""


class DriverInformation(BaseModel):

    lane_preference: str = ""
    reference_number: str = ""
    load_posted_date: str = ""
    equipment: str = "Hotshot"
    authority: str = "3 months"
    necessary_equipment: str = "Straps"
    desired_rate: str = ""
    hazmat_approved: str = "No"
    container_loads_capability: str = "No"
    bonded_carrier: str = "No"
    tanker_endorsment: str = "No"
    twic_card: str = ""


class ModelPrompts(BaseModel):

    SYSTEM_PROMPT: str = DEFAULT_SYSTEM_PROMPT

    FIRST_MESSAGE: str = DEFAULT_FIRST_MESSAGE

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value
