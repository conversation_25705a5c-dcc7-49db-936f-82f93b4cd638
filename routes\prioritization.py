import json
from fastapi import Request, UploadFile, File
from fastapi import APIRouter
from exceptions import ValidationError
from scores import score

prioritization_router = APIRouter()


@prioritization_router.post("/prioritization")
async def prioritization(
    request: Request,
    loads_data: UploadFile = File(
        None,
        description="JSON file containing the loads data.",
    ),
) -> dict:
    """
    Prioritizes a list of loads data.

    Args:
        loads_data (UploadFile): The JSON file containing the loads data.

    Returns:
        dict: A dictionary containing the prioritized loads data.
    """

    if not loads_data.filename.lower().endswith(".json"):
        raise ValidationError("Invalid file type. Only json file is accepted.")

    loads_data_content = await loads_data.read()

    try:
        loads_data_json = json.loads(loads_data_content)
    except json.JSONDecodeError:
        raise ValidationError("Invalid JSON File.")

    loads_data_json_scored = await score(loads_data_json)
    return loads_data_json_scored
