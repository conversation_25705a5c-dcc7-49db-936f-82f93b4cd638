from constants import (
    VOGENT_API,
    VOGENT_AUTH_KEY,
    VOGENT_ASSISTANT_ID,
    VOGENT_PHONE_NUM_ID,
    VOGENT_VERSION_MODEL_ID,
    VOGENT_TIMEOUT_MINUTES,
)
import requests
import json
from enums import LoadData, DriverInformation
from utils import get_prompt_placeholders_dict
from fastapi import HTTPException

# async def get_call_logs_from_vogent(last_k_calls: int) -> dict:
#     """
#     Fetches a list of calls from the Vogent API.

#     Returns:
#         dict: A dictionary containing the list of calls.
#     """
#     headers = {"Authorization": f"Bearer {VOGENT_AUTH_KEY}", "Content-Type": "application/json"}

#     response = requests.request(method="GET", url=VOGENT_API + "/call", headers=headers)

#     return json.loads(response.text)[-last_k_calls:]


async def get_call_log_from_vogent(call_id: str) -> dict:
    """
    Fetches details of a specific call from the Vogent API using the call ID.

    Args:
        call_id (str): The ID of the call to fetch details for.

    Returns:
        dict: A dictionary containing the details of the specified call.
    """
    headers = {
        "Authorization": f"Bearer {VOGENT_AUTH_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.request(
        method="GET", url=VOGENT_API + "/dials/" + call_id, headers=headers
    )

    if response.status_code != 200:
        raise HTTPException(
            status_code=response.status_code,
            detail=f"Error getting call details with Vogent API: {response.text}",
        )

    return json.loads(response.text)


async def make_call_with_vogent(
    number: str,
    load_data: LoadData,
    driver_information: DriverInformation,
    agent_name: str,
) -> dict:
    """
    Initiates a call using the Vogent API with the provided phone number, prompts, and load data.

    Args:
        number (str): The phone number to call.
        prompts (ModelPrompts): The prompts to use for the call.
        load_data (str): The load data to include in the call.
        driver_information (DriverInformation): The driver information to include in the call.
        agent_name (str): The name of the agent to use for the call.
    Returns:
        dict: A dictionary containing the response from the Vogent API.
    """

    prompt_placeholders = await get_prompt_placeholders_dict(
        load_data, driver_information, agent_name
    )

    payload = {
        "callAgentId": VOGENT_ASSISTANT_ID,
        "browserCall": False,
        "toNumber": number,
        "fromNumberId": VOGENT_PHONE_NUM_ID,
        "callAgentInput": prompt_placeholders,
        "timeoutMinutes": VOGENT_TIMEOUT_MINUTES,
        "versionedModelId": VOGENT_VERSION_MODEL_ID,
    }
    headers = {
        "Authorization": f"Bearer {VOGENT_AUTH_KEY}",
        "Content-Type": "application/json",
    }
    response = requests.request(
        "POST", VOGENT_API + "/dials", json=payload, headers=headers
    )

    if response.status_code != 200:
        raise HTTPException(
            status_code=response.status_code,
            detail=f"Error making call with Vogent API: {response.text}",
        )

    return json.loads(response.text)
