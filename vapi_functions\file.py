from constants import VAPI_API, VAPI_AUTH_KEY
import requests
import json
from fastapi import File


async def get_files_from_vapi() -> dict:
    """
    Fetches a list of files from the VAPI API.

    Returns:
        dict: A dictionary containing the list of files.
    """
    headers = {"Authorization": VAPI_AUTH_KEY}

    response = requests.request(method="GET", url=VAPI_API + "/file", headers=headers)

    return json.loads(response.text)


async def upload_file_to_vapi(file: File) -> dict:
    """
    Uploads a file to the VAPI API.

    Args:
        file (File): The file to upload.

    Returns:
        dict: A dictionary containing the response from the VAPI API.
    """
    file_content = await file.read()

    files = {"file": file_content}

    headers = {"Authorization": VAPI_AUTH_KEY}

    response = requests.request(
        method="POST", url=VAPI_API + "/file", files=files, headers=headers
    )

    return json.loads(response.text)
