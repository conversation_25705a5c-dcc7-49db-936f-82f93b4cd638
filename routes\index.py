import logging
from constants import INSTANCE_TYPE, API_VERSION
from fastapi import APIRouter

index_router = APIRouter()


@index_router.get("/v1/health")
async def index() -> dict:
    """
    Returns the status of the API.

    Returns:
        dict: A dictionary containing the status of the API.
    """
    logging.info("Index is working")
    return {
        "Status": True,
        "message": "Xtrucker AI API is up and running!",
        "instance": INSTANCE_TYPE,
        "version": API_VERSION,
    }
