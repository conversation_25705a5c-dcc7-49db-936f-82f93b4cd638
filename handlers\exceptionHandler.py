import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse


def all_exception_handler(_, e: Exception) -> JSONResponse:
    """
    Handles all exceptions by logging the error and returning a JSON response.

    Args:
        _: The request object (unused).
        e: The exception to handle.

    Returns:
        JSONResponse: A JSON response containing the error message and status code.
    """
    logging.exception(f"Error caught in all_exception_handler")

    return JSONResponse(
        content={"error": str(e)},
        status_code=(
            e.status_code
            if hasattr(e, "status_code")
            else status.HTTP_500_INTERNAL_SERVER_ERROR
        ),
    )


async def all_http_exception_handler(_, e: HTTPException) -> JSONResponse:
    """
    Handles HTTP exceptions by logging the error and returning a JSON response.

    Args:
        _: The request object (unused).
        e: The HTTP exception to handle.

    Returns:
        JSONResponse: A JSON response containing the error message and status code.
    """
    logging.exception(f"Error caught in all_http_exception_handler")
    return J<PERSON><PERSON><PERSON>ponse(
        content={"error": e.detail},
        status_code=e.status_code
    )


async def request_validation_exception_handler(_, e: RequestValidationError) -> JSONResponse:
    """
    Handles request validation exceptions by logging the error and returning a JSON response.

    Args:
        _: The request object (unused).
        e: The request validation exception to handle.

    Returns:
        JSONResponse: A JSON response containing the error message and status code.
    """
    logging.exception(f"Error caught in request_validation_exception_handler")
    return JSONResponse(
        content={"error": f"The API expects {e.errors()[0]['loc'][1]}."},  # Updated to use e.errors() method
        status_code=status.HTTP_400_BAD_REQUEST,
    )
