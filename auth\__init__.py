import base64
import logging
import secrets
from fastapi import Depends
from fastapi.security import <PERSON><PERSON>P<PERSON>asic, HTTPBasicCredentials
from exceptions import UnauthorizedError
from constants import BASIC_AUTH_KEY

security = HTTPBasic()

def authenticate_request(credentials: HTTPBasicCredentials = Depends(security)) -> str:
    """
    Authenticates the request using the provided credentials.

    Args:
        credentials (HTTPBasicCredentials): The credentials to authenticate.

    Returns:
        str: The username if authentication is successful.
    """
    auth_value = f"{base64.b64encode(f'{credentials.username}:{credentials.password}'.encode()).decode()}"
    if not secrets.compare_digest(auth_value, BASIC_AUTH_KEY):
        logging.error(f"Not authenticated! {credentials.username}")
        raise UnauthorizedError("Not authenticated")
    return credentials.username
