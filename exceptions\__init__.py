from fastapi import HTTPException, status


class ValidationError(HTTPException):
    """
    Custom HTTPException for validation errors.
    """
    def __init__(self, message):
        """
        Initializes the ValidationError with the provided message.

        Args:
            message (str): The error message.
        """
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=message)


class UnauthorizedError(HTTPException):
    """
    Custom HTTPException for unauthorized errors.
    """
    def __init__(self, message):
        """
        Initializes the UnauthorizedError with the provided message.

        Args:
            message (str): The error message.
        """
        super().__init__(status_code=status.HTTP_401_UNAUTHORIZED, detail=message)


class CustomError(HTTPException):
    """
    Custom HTTPException for custom errors.
    """
    def __init__(self, message, status_code):
        """
        Initializes the CustomError with the provided message and status code.

        Args:
            message (str): The error message.
            status_code (int): The HTTP status code.
        """
        super().__init__(status_code=status_code, detail=message)
