# VAPI Truck Dispatch Workflow

This repository contains a VAPI workflow designed for truck dispatching automation. The workflow handles calls to freight brokers, detects IVR systems, and gathers load information with rate negotiation.

## Workflow Overview

The workflow consists of two main nodes:

### 1. IVR Detector Node (Start Node)
- **Purpose**: Detects if the call is connected to an IVR system or a human broker
- **Actions**:
  - If IVR detected: Uses DTMF tool to press appropriate keys to reach a broker
  - If human detected: Silently transfers to Load Gathering Node
- **Tools**: DTMF, TransferCall

### 2. Load Gathering Node
- **Purpose**: Confirms load availability, gathers load details, and negotiates rates
- **Process**:
  1. Confirm load availability (end call if none available)
  2. Gather load information (pickup/delivery locations, dates, load type, etc.)
  3. Negotiate rates (target $2.50+ per mile, minimum $2.00 per mile)
  4. Finalize details and end call professionally
- **Variable Extraction**: Captures all relevant load and broker information
- **Tools**: EndCall

## Files Included

1. **`vapi-truck-dispatch-workflow.json`** - The complete workflow configuration
2. **`create_vapi_workflow.py`** - Python script to create the workflow via VAPI API
3. **`VAPI_WORKFLOW_README.md`** - This documentation file

## Setup Instructions

### Prerequisites
- VAPI account with API access
- Python 3.6+ installed
- `requests` library (`pip install requests`)

### Step 1: Get Your VAPI API Token
1. Log into your VAPI dashboard
2. Navigate to API settings
3. Copy your API token

### Step 2: Set Environment Variable
```bash
export VAPI_API_TOKEN='your_token_here'
```

### Step 3: Install Dependencies
```bash
pip install requests
```

### Step 4: Create the Workflow
```bash
python create_vapi_workflow.py
```

## Workflow Configuration Details

### IVR Detector Node Configuration
- **Model**: GPT-4o with low temperature (0.1) for consistent detection
- **Voice**: Azure Andrew voice
- **Prompt**: Specialized for IVR vs human detection
- **Tools**: DTMF for keypad navigation, TransferCall for routing

### Load Gathering Node Configuration
- **Model**: GPT-4o with moderate temperature (0.3) for natural conversation
- **Voice**: Azure Andrew voice
- **Prompt**: Professional truck dispatcher persona with negotiation guidelines
- **Variable Extraction**: Comprehensive schema capturing all load details
- **Tools**: EndCall for conversation termination

### Extracted Variables
The workflow extracts the following variables:
- `load_available` (boolean)
- `pickup_location` (string)
- `delivery_location` (string)
- `pickup_date` (string)
- `delivery_date` (string)
- `load_type` (string)
- `weight` (string)
- `equipment_type` (enum: dry_van, flatbed, reefer, step_deck, other)
- `rate_per_mile` (number)
- `total_rate` (number)
- `broker_name` (string)
- `broker_company` (string)
- `load_reference` (string)
- `negotiation_successful` (boolean)

## Usage Tips

### Rate Negotiation Guidelines
- Target rate: $2.50+ per mile
- Minimum acceptable: $2.00 per mile
- Consider deadhead miles, fuel costs, and timing
- Be professional but firm in negotiations

### IVR Navigation
- Common IVR options: Press 1 for sales/broker, Press 2 for dispatch
- The system will automatically detect and navigate
- Fallback to human transfer if IVR navigation fails

### Call Flow
1. Call connects → IVR Detector activates
2. If IVR: Navigate using DTMF → Transfer to Load Gathering
3. If Human: Direct transfer to Load Gathering
4. Load Gathering: Confirm availability → Gather info → Negotiate → End call

## Customization

### Modifying Rate Targets
Edit the prompt in the Load Gathering Node to adjust rate expectations:
```json
"prompt": "...Our target rate is $X.XX+ per mile..."
```

### Adding New Variables
Extend the `variableExtractionPlan.schema.properties` in the Load Gathering Node.

### Changing Voice/Model
Update the `voice` and `model` configurations in each node as needed.

## Troubleshooting

### Common Issues
1. **API Token Error**: Ensure VAPI_API_TOKEN is set correctly
2. **File Not Found**: Ensure all files are in the same directory
3. **Rate Limit**: VAPI may have rate limits; wait and retry if needed

### Testing the Workflow
1. Use VAPI dashboard to test the workflow
2. Make test calls to verify IVR detection
3. Test with both IVR and human scenarios
4. Verify variable extraction is working correctly

## Support

For VAPI-specific issues, refer to:
- [VAPI Documentation](https://docs.vapi.ai/)
- [VAPI Support](https://docs.vapi.ai/support)

For workflow customization questions, modify the configuration files as needed based on your specific requirements.
