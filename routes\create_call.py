from fastapi import Request, Body, Query
from fastapi import APIRouter
from constants import (
    DEFAULT_AGENT_NAME,
    DEFAULT_PLATFORM,
)
import logging
from enums import ModelPrompts, LoadData, DriverInformation
from vapi_functions.call import make_call
from vogent_ai.call import make_call_with_vogent
from utils import inject_variables_in_prompt
import markdown2

create_call_router = APIRouter()


@create_call_router.post("/create-call")
async def create_call(
    request: Request,
    number: str = Query(
        ...,
        description="Phone number of the broker.",
    ),
    agent_name: str = Query(
        DEFAULT_AGENT_NAME,
        description="Agent Name.",
    ),
    load_data: LoadData = Body(
        None,
        description="Load Data.",
    ),
    driver_information: DriverInformation = Body(
        None,
        description="Driver Information.",
    ),
    model_prompts: ModelPrompts = Body(
        None,
        description="System and First Message for the AI.",
    ),
    platform: str = Query(
        DEFAULT_PLATFORM,
        description="Platform to use for the call.",
        enum=["vogent", "vapi"],
    ),
    return_injected_prompt: str = Query(
        "false",
        description="System and First Message for the AI.",
        enum=["true", "false"],
    ),
) -> dict:
    """
    Creates a call to the VAPI API.

    Args:
        number (str): The phone number of the dispatcher.
        load_data (str): The load data to be used for the call.
        model_prompts (ModelPrompts): The system and first message for the AI.
        platform (str): The platform to use for the call.
        return_injected_prompt (str): Whether to return the injected prompt or not.

    Returns:
        dict: A dictionary containing the call data.
    """
    if platform == "vogent":
        logging.info("Making call with Vogent.")
        response = await make_call_with_vogent(
            number,
            load_data,
            driver_information if driver_information else DriverInformation(),
            agent_name,
        )
    else:
        logging.info("Injecting variables into Prompt.")
        system_prompt, first_message = await inject_variables_in_prompt(
            model_prompts,
            load_data,
            driver_information if driver_information else DriverInformation(),
            agent_name,
        )
        if return_injected_prompt == "true":
            return {
                "system_prompt": markdown2.markdown(system_prompt).replace("\n", "")
            }
        logging.info(f"Creating call for number: {number}")
        response = await make_call(number, system_prompt, first_message)
    return response
