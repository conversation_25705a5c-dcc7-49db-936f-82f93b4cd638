from enums import Model<PERSON>rom<PERSON>, LoadData, DriverInformation
from typing import Optional


async def clean_data(load_data: dict) -> dict:
    """
    Cleans and converts string data in the 'loads_data_json' dictionary to appropriate float values.

    Args:
        loads_data_json (dict): A dictionary containing load data with string values that need conversion.

    Returns:
        dict: The modified dictionary with cleaned and converted float values.
    """
    try:
        load_data["deadhead"] = float(load_data["deadhead"].replace("mi", "").strip())
        load_data["perMilePrice"] = float(
            load_data["perMilePrice"].replace("$", "").replace("/mi", "").strip()
        )
        load_data["price"] = float(
            load_data["price"].replace("$", "").replace(",", "").strip()
        )
        load_data["mileage"] = float(
            load_data["mileage"].replace("mi", "").replace(",", "").strip()
        )
    except:
        pass

    return load_data


async def get_model_prompts(messages: Optional[ModelPrompts] = None) -> tuple:
    """
    Retrieves or initializes model prompts from the 'messages'.

    Args:
        messages (ModelPrompts): A Enum of ModelPrompts that contains message prompts. If empty, default prompts are used.

    Returns:
        tuple: A tuple containing the system prompt and the first message.
    """
    # Pick the prompts if not received
    if not messages:
        messages = ModelPrompts()
        system_prompt = messages.SYSTEM_PROMPT
        first_message = messages.FIRST_MESSAGE
    else:
        system_prompt = messages.SYSTEM_PROMPT
        first_message = messages.FIRST_MESSAGE

    return system_prompt, first_message


async def calculate_arrival_time(deadhead_miles: int):

    speed = 50
    arrival_time_in_hours = deadhead_miles / speed

    # Convert arrival time to hours and minutes
    hours = int(arrival_time_in_hours)
    minutes = int((arrival_time_in_hours - hours) * 60)

    # Format the arrival time as a string
    if hours > 0:
        arrival_time = f"{hours} hour{'s' if hours > 1 else ''} {minutes} minute{'s' if minutes != 1 else ''}"
    else:
        arrival_time = f"{minutes} minute{'s' if minutes != 1 else ''}"

    return arrival_time


async def inject_variables_in_prompt(
    prompts: ModelPrompts,
    load_data: LoadData,
    driver_information: DriverInformation,
    agent_name: str,
) -> list[str, str]:

    system_prompt, first_message = await get_model_prompts(prompts)
    prompt_placeholders = await get_prompt_placeholders_dict(
        load_data, driver_information, agent_name
    )
    system_prompt = system_prompt.format(**prompt_placeholders)
    return system_prompt, first_message


async def get_prompt_placeholders_dict(
    load_data: LoadData,
    driver_information: DriverInformation,
    agent_name: str,
) -> dict:

    load_data = load_data.model_dump()
    load_data["broker_mc"] = load_data["broker_mc"].replace("MC#", "")
    driver_information = driver_information.model_dump()
    try:
        deadhead_miles = float(load_data["deadhead_miles"])
        arrive_time = await calculate_arrival_time(deadhead_miles)
    except:
        arrive_time = ""
    additional_params = {"agent_name": agent_name, "arrival_time": arrive_time}
    prompt_placeholders = {**load_data, **driver_information, **additional_params}
    return prompt_placeholders
