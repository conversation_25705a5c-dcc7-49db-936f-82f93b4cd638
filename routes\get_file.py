from fastapi import APIRouter
from vapi_functions.file import get_files_from_vapi
import logging

get_file_router = APIRouter()


@get_file_router.post("/get-files")
async def get_files() -> dict:
    """
    Fetches a list of files from the VAPI API.

    Returns:
        dict: A dictionary containing the list of files.
    """
    logging.info("Fetching files")
    response = await get_files_from_vapi()
    return response
