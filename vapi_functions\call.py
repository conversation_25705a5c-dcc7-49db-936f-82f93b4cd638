from constants import (
    VAPI_API,
    VAPI_AUTH_KEY,
    VAPI_ASSISTANT_ID,
    VAPI_PHONE_NUM_ID,
    DEFAULT_VOICE_MAIL_MESSAGE,
    AI_WAIT_BEFORE_SPEAK,
    SILENCE_TIMEOUT_SECONDS,
    MAX_DURATION_SECONDS,
    <PERSON>FAULT_TOOLS,
    TRANSCRIP<PERSON>ON_MODEL,
    TRANSCRIPTION_PROVIDER,
    TRANSCRIPTION_LANGUAGE,
    VOICE_PROVIDER,
    VOICE_ID,
    VOICE_MODEL,
    VOICE_INSTRUCTIONS,
    SPEAK_FIRST,
    LLM_MODEL,
    LLM_PROVIDER,
    LLM_TEMPERATURE,
)
import requests
import json


async def get_call_logs_from_vapi(last_k_calls: int) -> dict:
    """
    Fetches a list of calls from the VAPI API.

    Returns:
        dict: A dictionary containing the list of calls.
    """
    headers = {"Authorization": VAPI_AUTH_KEY, "Content-Type": "application/json"}

    response = requests.request(method="GET", url=VAPI_API + "/call", headers=headers)

    return json.loads(response.text)[-last_k_calls:]


async def get_call_log_from_vapi(call_id: str) -> dict:
    """
    Fetches details of a specific call from the VAPI API using the call ID.

    Args:
        call_id (str): The ID of the call to fetch details for.

    Returns:
        dict: A dictionary containing the details of the specified call.
    """
    headers = {"Authorization": VAPI_AUTH_KEY, "Content-Type": "application/json"}

    response = requests.request(
        method="GET", url=VAPI_API + "/call/" + call_id, headers=headers
    )

    return json.loads(response.text)


async def make_call(number: str, system_prompt: str, first_message: str) -> dict:
    """
    Initiates a call using the VAPI API with the provided phone number, prompts, and load data.

    Args:
        number (str): The phone number to call.
        prompts (ModelPrompts): The prompts to use for the call.
        load_data (str): The load data to include in the call.

    Returns:
        dict: A dictionary containing the response from the VAPI API.
    """
    if SPEAK_FIRST == "assistant-waits-for-user":
        first_message = None
    payload = {
        "assistantId": VAPI_ASSISTANT_ID,
        "phoneNumberId": VAPI_PHONE_NUM_ID,
        "customer": {
            "numberE164CheckEnabled": True,
            "extension": None,
            "number": number,
        },
        "assistant": {
            # "voicemailDetection": {
            #     "provider": "twilio",
            #     "voicemailDetectionTypes": [
            #         "machine_start",
            #         "machine_end_beep",
            #         "unknown",
            #     ],
            #     "enabled": True,
            #     "machineDetectionTimeout": 15,
            # },
            "model": {
                "provider": LLM_PROVIDER,
                "model": LLM_MODEL,
                "temperature": LLM_TEMPERATURE,
                "messages": [
                    {"role": "system", "content": system_prompt},
                ],
                "tools": DEFAULT_TOOLS,
            },
            "transcriber": {
                "provider": TRANSCRIPTION_PROVIDER,
                "model": TRANSCRIPTION_MODEL,
                # "language": TRANSCRIPTION_LANGUAGE,
            },
            "voice": {
                "provider": VOICE_PROVIDER,
                "voiceId": VOICE_ID,
                "model": VOICE_MODEL,
            },
            "firstMessageMode": SPEAK_FIRST,
            "firstMessage": first_message,
            "silenceTimeoutSeconds": SILENCE_TIMEOUT_SECONDS,
            "maxDurationSeconds": MAX_DURATION_SECONDS,
            "modelOutputInMessagesEnabled": True,
            "backgroundDenoisingEnabled": True,
            "backgroundSound": "off",
            "startSpeakingPlan": {
                "waitSeconds": AI_WAIT_BEFORE_SPEAK,
            },
            "voicemailMessage": DEFAULT_VOICE_MAIL_MESSAGE,
        },
    }

    if VOICE_PROVIDER == "openai" or VOICE_PROVIDER == "gpt-4o-mini-tts":
        payload["assistant"]["voice"]["instructions"] = VOICE_INSTRUCTIONS

    headers = {"Authorization": VAPI_AUTH_KEY, "Content-Type": "application/json"}

    response = requests.request(
        method="POST", url=VAPI_API + "/call", json=payload, headers=headers
    )

    return json.loads(response.text)
