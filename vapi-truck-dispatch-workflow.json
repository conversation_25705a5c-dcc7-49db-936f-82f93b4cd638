{"name": "Truck Dispatch IVR and Load Gathering Workflow", "nodes": [{"type": "conversation", "name": "IVR Detector Node", "isStart": true, "prompt": "You are an IVR detection specialist for a truck dispatching company. Your job is to determine if this call is connected to an Interactive Voice Response (IVR) system or if you're speaking directly to a human broker.\n\nListen carefully to the audio:\n- If you hear automated menu options, recorded messages, or prompts asking you to press numbers, this is an IVR system\n- If you hear a human voice greeting you or asking how they can help, this is a human\n- If you hear hold music or silence initially, wait a moment to determine what follows\n\nBased on what you detect:\n- If IVR: Use the DTMF tool to navigate to speak with a broker (typically press 1 for sales or broker)\n- If Human: Use the TransferCallTool to silently transfer to the Load Gathering Node\n\nBe quick and decisive in your detection. Do not engage in conversation - just detect and act.", "model": {"provider": "openai", "model": "gpt-4o", "temperature": 0.1}, "voice": {"provider": "azure", "voiceId": "andrew"}, "tools": [{"type": "dtmf"}, {"type": "transferCall", "destinations": [{"type": "workflow", "workflowId": "load-gathering-node"}]}], "metadata": {"description": "Detects if call is connected to IVR or human and routes accordingly"}}, {"type": "conversation", "name": "Load Gathering Node", "prompt": "You are a professional truck dispatcher negotiating loads with freight brokers. Your goal is to:\n\n1. CONFIRM LOAD AVAILABILITY: First, confirm if they have loads available for dispatch\n   - If NO loads available: Politely end the call\n   - If loads ARE available: Proceed to gather information\n\n2. GATHER LOAD INFORMATION:\n   - Pickup location and date/time\n   - Delivery location and date/time  \n   - Load type and weight\n   - Equipment type needed (dry van, flatbed, reefer, etc.)\n   - Rate offered per mile or total\n   - Any special requirements\n\n3. NEGOTIATE RATES:\n   - Our target rate is $2.50+ per mile\n   - Be professional but firm in negotiations\n   - Consider factors like deadhead miles, fuel costs, and timing\n   - Don't accept rates below $2.00 per mile unless exceptional circumstances\n\n4. FINALIZE DETAILS:\n   - Confirm all pickup and delivery details\n   - Get broker contact information and load reference number\n   - Confirm rate-con will be sent\n   - Thank them and end the call professionally\n\nBe professional, efficient, and focused on securing profitable loads for our trucking operation.", "model": {"provider": "openai", "model": "gpt-4o", "temperature": 0.3}, "voice": {"provider": "azure", "voiceId": "andrew"}, "variableExtractionPlan": {"schema": {"type": "object", "properties": {"load_available": {"type": "boolean", "description": "Whether loads are available"}, "pickup_location": {"type": "string", "description": "Pickup city and state"}, "delivery_location": {"type": "string", "description": "Delivery city and state"}, "pickup_date": {"type": "string", "description": "Pickup date and time"}, "delivery_date": {"type": "string", "description": "Delivery date and time"}, "load_type": {"type": "string", "description": "Type of freight being hauled"}, "weight": {"type": "string", "description": "Weight of the load"}, "equipment_type": {"type": "string", "enum": ["dry_van", "flatbed", "reefer", "step_deck", "other"], "description": "Type of trailer equipment needed"}, "rate_per_mile": {"type": "number", "description": "Rate offered per mile in dollars"}, "total_rate": {"type": "number", "description": "Total rate for the load in dollars"}, "broker_name": {"type": "string", "description": "Name of the broker contact"}, "broker_company": {"type": "string", "description": "Broker company name"}, "load_reference": {"type": "string", "description": "Load reference or confirmation number"}, "negotiation_successful": {"type": "boolean", "description": "Whether rate negotiation was successful"}}, "required": ["load_available"]}}, "tools": [{"type": "endCall"}], "metadata": {"description": "Gathers load information and negotiates rates with brokers"}}], "edges": [{"from": "IVR Detector Node", "to": "Load Gathering Node", "condition": {"type": "ai", "prompt": "User detected a human voice or the IVR navigation was completed successfully"}, "metadata": {"description": "Route to load gathering when human is detected or IVR is navigated"}}], "globalPrompt": "You are part of a truck dispatching automation system. Always be professional, efficient, and focused on the trucking industry. Speak clearly and at an appropriate pace for phone conversations.", "voice": {"provider": "azure", "voiceId": "andrew", "speed": 1.0}, "transcriber": {"provider": "assembly-ai", "language": "en"}}