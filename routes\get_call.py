from fastapi import Request, Query
from fastapi import APIRouter
import logging
from vapi_functions.call import get_call_log_from_vapi, get_call_logs_from_vapi
from vogent_ai.call import get_call_log_from_vogent
from constants import DEFAULT_PLATFORM
from fastapi import HTTPException

get_call_router = APIRouter()


@get_call_router.post("/get-call")
async def get_calls(
    request: Request,
    call_id: str = Query(
        None,
        description="Call Id to look data for.",
    ),
    last_k_calls: int = Query(
        10,
        description="Number of last calls to fetch.",
    ),
    platform: str = Query(
        DEFAULT_PLATFORM,
        description="Platform to use for the call.",
        enum=["vogent", "vapi"],
    ),
) -> dict:
    """
    Fetches a call from the VAPI API.

    Args:
        call_id (str): The ID of the call to fetch.
        last_k_calls (int): The number of last calls to fetch.
        platform (str): The platform to use for the call.

    Returns:
        dict: A dictionary containing the call data.
    """

    if platform == "vogent":
        if call_id:
            logging.info(f"Vogent: Fetching call data for call_id: {call_id}")
            response = await get_call_log_from_vogent(call_id)
            return response
        else:
            raise HTTPException(
                status_code=400,
                detail="Vogent doesn't support list all calls. Call ID is required.",
            )

    if call_id:
        logging.info(f"VAPI: Fetching call data for call_id: {call_id}")
        response = await get_call_log_from_vapi(call_id)
        return response

    logging.info(f"VAPI: Fetching last {last_k_calls} calls.")
    response = await get_call_logs_from_vapi(last_k_calls)
    return response
