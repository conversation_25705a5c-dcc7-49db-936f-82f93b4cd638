from fastapi import <PERSON><PERSON><PERSON>, HTTPException
import logging
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from constants import API_VERSION
from mangum import Mangum
from handlers.exceptionHandler import (
    all_exception_handler,
    all_http_exception_handler,
    request_validation_exception_handler,
)
from routes import *
import uvicorn

# Set up logging
logging.basicConfig(
    format="%(asctime)s [%(funcName)s] %(levelname)s: %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

# Create FastAPI instance
app = FastAPI(
    title="Xtrucker AI API",
    description="This API is for automating the call system of truck dispatching.",
    version=API_VERSION,
    swagger_ui_parameters={"syntaxHighlight": False},
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

handler = Mangum(app)

app.add_exception_handler(Exception, all_exception_handler)
app.add_exception_handler(HTTPException, all_http_exception_handler)
app.add_exception_handler(RequestValidationError, request_validation_exception_handler)


app.middleware("http")(http_middleware)

routers = [
    index_router,
    create_call_router,
    get_call_router,
    prioritization_router,
    get_file_router,
    upload_file_router,
]

for router in routers:
    app.include_router(router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
