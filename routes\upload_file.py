from fastapi import Request, UploadFile, File
from fastapi import APIRouter
from vapi_functions.file import upload_file_to_vapi

upload_file_router = APIRouter()


@upload_file_router.post("/upload-file")
async def upload_file(
    request: Request,
    file: UploadFile = File(
        None,
        description="This is the File you want to upload for use with the Knowledge Base.",
    ),
) -> dict:
    """
    Uploads a file to the VAPI API.

    Args:
        file (UploadFile): The file to upload.

    Returns:
        dict: A dictionary containing the file data.
    """
    response = await upload_file_to_vapi(file)
    return response
