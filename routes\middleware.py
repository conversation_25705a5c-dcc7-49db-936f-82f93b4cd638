from fastapi import Request
from constants import (
    DISABLE_IP_WHITELIST,
    IP_WHITELIST,
)
import logging
import time
from exceptions import UnauthorizedError


async def http_middleware(request: Request, call_next):
    start_time = time.time()

    client_host = request.client.host
    url_path = request.url.path
    logging.info(f"Received a request: {request.method} | {url_path}")

    if DISABLE_IP_WHITELIST == "false" and client_host not in IP_WHITELIST:
        logging.error(f"Unauthorized access for {client_host}!")
        raise UnauthorizedError("Unauthorized access")

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
