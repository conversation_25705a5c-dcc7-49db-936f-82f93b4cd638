import math
from constants import (
    MAX_SCORE,
    DECAY_RATE,
    DEADHEAD_FROM_WEIGHT,
    TOTAL_PRICE_WEIGHT,
    PER_MILE_WEIGHT,
    TOTAL_PER_MILE_WEIGHT
)
from utils import clean_data

async def score(loads_data_json: dict) -> dict:
    """
    Calculate scores for a dictionary of load data.

    Args:
        loads_data_json (dict): A dictionary containing load data.

    Returns:
        dict: A dictionary with load IDs and their corresponding scores.
    """

    for load_data in loads_data_json["load_data"]:

        cleaned_load_data = await clean_data(load_data.copy())

        score = {}

        score["per_mile_rate"] = {
            "score": await get_per_mile_rate_score(cleaned_load_data['perMilePrice']),
            "weight": PER_MILE_WEIGHT
        }

        score["dead_head"] = {
            "score": await get_dead_head_from_score(cleaned_load_data["deadhead"]),
            "weight": DEADHEAD_FROM_WEIGHT
        }

        score["price"] = {
            "score": await get_total_price_score(cleaned_load_data["price"]),
            "weight": TOTAL_PRICE_WEIGHT
        }

        total_pmr = await get_price_per_total_mileage(cleaned_load_data)

        score["total_per_mile_rate"] = {
            "score": await get_total_per_mile_rate_score(total_pmr),
            "weight": TOTAL_PER_MILE_WEIGHT
        }
        
        load_data["score"] = {
            "total_score": await get_total_score(score),
            "score_breakdown": score
        }
    
    sorted_loads_data_json = await sort_loads(loads_data_json)  

    return sorted_loads_data_json

async def sort_loads(loads_data_json: dict) -> dict:
    """
    Sort the loads data by the total score.

    Args:
        loads_data_json (dict): A dictionary containing load data.

    Returns:
        dict: A dictionary with load IDs and their corresponding scores.
    """
    
    # Access the 'load_data' list within the dictionary
    sorted_load_data = sorted(loads_data_json["load_data"], key=lambda x: x['score']['total_score'], reverse=True)
    
    # Update the original dictionary with the sorted list
    loads_data_json["load_data"] = sorted_load_data
    
    return loads_data_json

async def get_total_score(score: dict) -> float:
    """
    Calculate the total score based on individual scores and their weights.

    Args:
        score (dict): A dictionary containing individual scores and their weights.

    Returns:
        float: The total weighted score.
    """
    sum_score = 0
    for factor in score:
        sum_score += score[factor]['score'] * score[factor]['weight']
    return sum_score

async def get_per_mile_rate_score(per_mile_rate: float) -> float:
    """
    Calculate the score for the per mile rate.

    Args:
        per_mile_rate (float): The per mile rate value.

    Returns:
        float: The calculated score for the per mile rate.
    """
    return round(MAX_SCORE * math.exp(DECAY_RATE * per_mile_rate), 3)

async def get_dead_head_from_score(dead_head_from: float) -> float:
    """
    Calculate the score for the deadhead from distance.

    Args:
        dead_head_from (float): The deadhead from distance.

    Returns:
        float: The calculated score for the deadhead from distance.
    """
    return round(MAX_SCORE * math.exp(-DECAY_RATE * dead_head_from), 3)

async def get_total_price_score(price: float) -> float:
    """
    Calculate the score for the total price.

    Args:
        price (float): The total price.

    Returns:
        float: The calculated score for the price.
    """
    return round(MAX_SCORE * math.exp(DECAY_RATE * price), 3)

async def get_total_per_mile_rate_score(total_pmr: float) -> float:
    """
    Calculate the score for the total per mile rate.

    Args:
        total_pmr (float): The total per mile rate value.

    Returns:
        float: The calculated score for the total per mile rate.
    """
    return round(MAX_SCORE * math.exp(DECAY_RATE * total_pmr), 3)

async def get_price_per_total_mileage(load_data: dict) -> float:
    """
    Calculate the price per total mileage.

    Args:
        load_data (dict): A dictionary containing load data.

    Returns:
        float: The price per total mileage.
    """
    return load_data["price"] / (load_data["deadhead"] + load_data["mileage"])