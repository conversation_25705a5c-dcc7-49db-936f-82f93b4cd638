#!/usr/bin/env python3
"""
VAPI Truck Dispatch Workflow Creator

This script creates a VAPI workflow for truck dispatching automation with:
1. IVR Detector Node - Detects if call is connected to IVR or human
2. Load Gathering Node - Gathers load information and negotiates rates

Usage:
    python create_vapi_workflow.py

Requirements:
    - VAPI API token set as environment variable: VAPI_API_TOKEN
    - requests library: pip install requests
"""

import os
import json
import requests
from typing import Dict, Any

class VAPIWorkflowCreator:
    def __init__(self, api_token: str):
        self.api_token = api_token
        self.base_url = "https://api.vapi.ai"
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }
    
    def create_workflow(self, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a workflow using the VAPI API
        
        Args:
            workflow_config: The workflow configuration dictionary
            
        Returns:
            The created workflow response from VAPI
        """
        url = f"{self.base_url}/workflow"
        
        try:
            response = requests.post(url, headers=self.headers, json=workflow_config)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating workflow: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            raise
    
    def load_workflow_config(self, config_file: str) -> Dict[str, Any]:
        """Load workflow configuration from JSON file"""
        with open(config_file, 'r') as f:
            return json.load(f)

def main():
    # Get API token from environment variable
    api_token = os.getenv('VAPI_API_TOKEN')
    if not api_token:
        print("Error: VAPI_API_TOKEN environment variable not set")
        print("Please set your VAPI API token:")
        print("export VAPI_API_TOKEN='your_token_here'")
        return
    
    # Initialize the workflow creator
    creator = VAPIWorkflowCreator(api_token)
    
    try:
        # Load the workflow configuration
        print("Loading workflow configuration...")
        workflow_config = creator.load_workflow_config('vapi-truck-dispatch-workflow.json')
        
        # Create the workflow
        print("Creating VAPI workflow...")
        result = creator.create_workflow(workflow_config)
        
        print("✅ Workflow created successfully!")
        print(f"Workflow ID: {result.get('id')}")
        print(f"Workflow Name: {result.get('name')}")
        
        # Save the result for reference
        with open('workflow_creation_result.json', 'w') as f:
            json.dump(result, f, indent=2)
        print("📄 Full result saved to 'workflow_creation_result.json'")
        
    except FileNotFoundError:
        print("Error: vapi-truck-dispatch-workflow.json not found")
        print("Please ensure the workflow configuration file exists")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
